import DOMPurify from 'dompurify';
import collection from 'easter-egg-collection';
import { initNavbar, initFooter, getCurrentPage } from './services/uiService.js';
import { renderTimeline } from './services/timelineUiService.js';
// ? Configuration du site
const siteName = "Jefff.js";
document.addEventListener('DOMContentLoaded', () => {
    const currentPage = getCurrentPage();
    console.log('Page actuelle détectée:', currentPage);
    // Initialiser l'UI de base
    initNavbar('#navbar-container', siteName);
    initFooter('#footer-container', siteName);
    if (currentPage === 'timeline') {
        console.log('Initialisation de la timeline sur la page timeline');
        renderTimeline('#timeline');
    }
    if (currentPage === 'login') {
        console.log('Initialisation de la timeline sur la page login');
        // renderTimeline('#timeline');
    }
});
// ? Initialisation de l'interface utilisateur







