/**
 * Service de validation de formulaires avec regex et affichage d'erreurs en temps réel
 * Utilise DaisyUI pour les styles d'erreur et de succès
 */
export function mailValidateInput(emailID) {
    const emailInput = document.getElementById(emailID);
    // console.log(emailInput);
    const regexMail = /^[a-z0-9._-]+@[a-z0-9._-]+\.[a-z]{2,6}$/; 
    emailInput.addEventListener('keyup', () => {
        if (regexMail.test(emailInput.value)) {
            emailInput.style.backgroundColor = 'lightgreen';
        } else {
            emailInput.style.backgroundColor = 'red';
        }
    }); 
};

export function passwordValidateInput(passwordID, errorID ) {
    const passwordInput = document.getElementById(passwordID);
    const passwordError = document.getElementById(errorID);
    let errorMessage = '';
    const errors = [];

    const regexDecimal = /\d/;
    const regexSpecialChar = /[$&@!]/;
    // const regexSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/;
    passwordInput.addEventListener('keyup', () => {
        if (passwordInput.value.length < 6) {
            passwordInput.style.backgroundColor = 'red';
            errors.push(`<li class="label">Mot de passe trop Court (au moins 6 caractères)</li>`);
        }
        else if(passwordInput.value.length > 8){
            passwordInput.style.backgroundColor = 'red';
            errors.push(`<li class="label">Mot de passe trop Long (maximum 8 caractères)</li>`);
        }
        if(errorMessage !==''){
            passwordInput.style.backgroundColor = 'red';
            passwordError.innerHTML = `${errorMessage}`;
        }
        if(!passwordInput.value.match(regexDecimal)){
            passwordInput.style.backgroundColor = 'red';
            errors.push( `<li class="label">Mot de passe doit contenir au moins un chiffre</li>`);
        }
        if(!passwordInput.value.match(regexSpecialChar)){
            passwordInput.style.backgroundColor = 'red';
            errors.push(`<li class="label">Mot de passe doit contenir au moins un caractère spécial ($, &, @, !)</li>`);
        }
        else {
            passwordInput.style.backgroundColor = 'lightgreen';
            passwordError.innerHTML = 'Mot de passe Valide !';
        }
    });
}

/**
 * Fonction améliorée de validation de mot de passe en temps réel
 * @param {string} passwordID - ID du champ mot de passe
 * @param {string} errorID - ID du conteneur d'erreurs
 * @param {Object} options - Options de configuration
 */
// export function passwordValidateInput(passwordID, errorID, options = {}) {
//     const {
//         minLength = 6,
//         maxLength = 50,
//         requireDigit = true,
//         requireSpecialChar = true,
//         requireUppercase = false,
//         requireLowercase = false,
//         specialChars = '$&@!',
//         showStrengthIndicator = true,
//         debounceTime = 300
//     } = options;

//     const passwordInput = document.getElementById(passwordID);
//     const errorContainer = document.getElementById(errorID);

//     if (!passwordInput || !errorContainer) {
//         console.error(`Éléments non trouvés: ${passwordID} ou ${errorID}`);
//         return;
//     }

//     // Regex patterns
//     const regexDigit = /\d/;
//     const regexSpecialChar = new RegExp(`[${specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`);
//     const regexUppercase = /[A-Z]/;
//     const regexLowercase = /[a-z]/;

//     let debounceTimer;

//     // Fonction de validation
//     function validatePassword() {
//         const value = passwordInput.value;
//         const errors = [];
//         let strength = 0;
//         let isValid = true;

//         // Reset styles
//         passwordInput.style.backgroundColor = '';
//         passwordInput.classList.remove('input-error', 'input-success');

//         // Si le champ est vide, on reset tout
//         if (value === '') {
//             errorContainer.innerHTML = '';
//             return;
//         }

//         // Vérification de la longueur
//         if (value.length < minLength) {
//             errors.push(`Mot de passe trop court (minimum ${minLength} caractères)`);
//             isValid = false;
//         } else if (value.length > maxLength) {
//             errors.push(`Mot de passe trop long (maximum ${maxLength} caractères)`);
//             isValid = false;
//         } else {
//             strength += 1;
//         }

//         // Vérification des chiffres
//         if (requireDigit && !regexDigit.test(value)) {
//             errors.push('Doit contenir au moins un chiffre');
//             isValid = false;
//         } else if (regexDigit.test(value)) {
//             strength += 1;
//         }

//         // Vérification des caractères spéciaux
//         if (requireSpecialChar && !regexSpecialChar.test(value)) {
//             errors.push(`Doit contenir au moins un caractère spécial (${specialChars})`);
//             isValid = false;
//         } else if (regexSpecialChar.test(value)) {
//             strength += 1;
//         }

//         // Vérification des majuscules
//         if (requireUppercase && !regexUppercase.test(value)) {
//             errors.push('Doit contenir au moins une majuscule');
//             isValid = false;
//         } else if (regexUppercase.test(value)) {
//             strength += 1;
//         }

//         // Vérification des minuscules
//         if (requireLowercase && !regexLowercase.test(value)) {
//             errors.push('Doit contenir au moins une minuscule');
//             isValid = false;
//         } else if (regexLowercase.test(value)) {
//             strength += 1;
//         }

//         // Affichage des résultats
//         displayValidationResult(value, errors, strength, isValid);
//     }

//     // Fonction d'affichage des résultats
//     function displayValidationResult(value, errors, strength, isValid) {
//         if (isValid) {
//             // Succès
//             passwordInput.style.backgroundColor = 'lightgreen';
//             passwordInput.classList.add('input-success');

//             errorContainer.innerHTML = `
//                 <div class="text-success text-sm mt-1 flex items-center">
//                     <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
//                     </svg>
//                     Mot de passe valide !
//                 </div>
//             `;
//         } else {
//             // Erreurs
//             passwordInput.style.backgroundColor = '#ffebee';
//             passwordInput.classList.add('input-error');

//             const errorList = errors.map(error => `
//                 <li class="flex items-start text-sm">
//                     <svg class="w-4 h-4 mr-1 mt-0.5 flex-shrink-0 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
//                     </svg>
//                     ${error}
//                 </li>
//             `).join('');

//             errorContainer.innerHTML = `
//                 <div class="text-error mt-1">
//                     <ul class="space-y-1">
//                         ${errorList}
//                     </ul>
//                 </div>
//             `;
//         }

//         // Indicateur de force (optionnel)
//         if (showStrengthIndicator && value.length > 0) {
//             const strengthIndicator = createStrengthIndicator(strength);
//             errorContainer.appendChild(strengthIndicator);
//         }
//     }

//     // Fonction pour créer l'indicateur de force
//     function createStrengthIndicator(strength) {
//         const maxStrength = 4; // Ajustez selon vos critères
//         const percentage = Math.min((strength / maxStrength) * 100, 100);

//         let strengthText = 'Très faible';
//         let strengthColor = '#ef4444'; // Rouge

//         if (percentage >= 75) {
//             strengthText = 'Fort';
//             strengthColor = '#22c55e'; // Vert
//         } else if (percentage >= 50) {
//             strengthText = 'Moyen';
//             strengthColor = '#f59e0b'; // Orange
//         } else if (percentage >= 25) {
//             strengthText = 'Faible';
//             strengthColor = '#f97316'; // Orange foncé
//         }

//         const indicator = document.createElement('div');
//         indicator.className = 'mt-2';
//         indicator.innerHTML = `
//             <div class="flex items-center justify-between text-xs mb-1">
//                 <span class="text-gray-600">Force du mot de passe</span>
//                 <span class="font-medium" style="color: ${strengthColor}">${strengthText}</span>
//             </div>
//             <div class="w-full bg-gray-200 rounded-full h-2">
//                 <div class="h-2 rounded-full transition-all duration-300"
//                      style="width: ${percentage}%; background-color: ${strengthColor}"></div>
//             </div>
//         `;

//         return indicator;
//     }

//     // Event listeners avec debounce pour de meilleures performances
//     passwordInput.addEventListener('input', () => {
//         clearTimeout(debounceTimer);
//         debounceTimer = setTimeout(validatePassword, debounceTime);
//     });

//     // Validation immédiate sur blur
//     passwordInput.addEventListener('blur', () => {
//         clearTimeout(debounceTimer);
//         validatePassword();
//     });

//     // Affichage des critères sur focus
//     passwordInput.addEventListener('focus', () => {
//         if (passwordInput.value === '') {
//             errorContainer.innerHTML = `
//                 <div class="text-gray-600 text-sm mt-1">
//                     <p class="font-medium mb-1">Le mot de passe doit contenir :</p>
//                     <ul class="list-disc list-inside space-y-0.5 text-xs">
//                         <li>Entre ${minLength} et ${maxLength} caractères</li>
//                         ${requireDigit ? '<li>Au moins un chiffre</li>' : ''}
//                         ${requireSpecialChar ? `<li>Au moins un caractère spécial (${specialChars})</li>` : ''}
//                         ${requireUppercase ? '<li>Au moins une majuscule</li>' : ''}
//                         ${requireLowercase ? '<li>Au moins une minuscule</li>' : ''}
//                     </ul>
//                 </div>
//             `;
//         }
//     });

//     // Validation initiale si le champ a déjà une valeur
//     if (passwordInput.value) {
//         validatePassword();
//     }
// }
// // Regex patterns pour la validation
// export const ValidationPatterns = {
//     email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
//     password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
//     passwordSimple: /^.{6,}$/, // Au moins 6 caractères
//     phone: /^(?:\+33|0)[1-9](?:[0-9]{8})$/,
//     name: /^[a-zA-ZÀ-ÿ\s'-]{2,50}$/,
//     username: /^[a-zA-Z0-9_-]{3,20}$/,
//     url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
//     zipCode: /^[0-9]{5}$/,
//     date: /^\d{4}-\d{2}-\d{2}$/
// };

// // Messages d'erreur personnalisés
// export const ValidationMessages = {
//     email: "Veuillez entrer une adresse email valide",
//     password: "Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial",
//     passwordSimple: "Le mot de passe doit contenir au moins 6 caractères",
//     phone: "Veuillez entrer un numéro de téléphone français valide",
//     name: "Le nom doit contenir entre 2 et 50 caractères (lettres, espaces, apostrophes et tirets uniquement)",
//     username: "Le nom d'utilisateur doit contenir entre 3 et 20 caractères (lettres, chiffres, _ et - uniquement)",
//     url: "Veuillez entrer une URL valide (http:// ou https://)",
//     zipCode: "Le code postal doit contenir exactement 5 chiffres",
//     date: "Veuillez entrer une date au format YYYY-MM-DD",
//     required: "Ce champ est obligatoire",
//     minLength: "Ce champ doit contenir au moins {min} caractères",
//     maxLength: "Ce champ ne peut pas dépasser {max} caractères",
//     match: "Les champs ne correspondent pas"
// };

// /**
//  * Valide un champ selon les règles spécifiées
//  * @param {string} value - Valeur à valider
//  * @param {Object} rules - Règles de validation
//  * @returns {Object} - Résultat de la validation {isValid: boolean, message: string}
//  */
// export function validateField(value, rules = {}) {
//     // Vérification si le champ est requis
//     if (rules.required && (!value || value.trim() === '')) {
//         return {
//             isValid: false,
//             message: ValidationMessages.required
//         };
//     }

//     // Si le champ n'est pas requis et est vide, c'est valide
//     if (!rules.required && (!value || value.trim() === '')) {
//         return {
//             isValid: true,
//             message: ''
//         };
//     }

//     // Validation de la longueur minimale
//     if (rules.minLength && value.length < rules.minLength) {
//         return {
//             isValid: false,
//             message: ValidationMessages.minLength.replace('{min}', rules.minLength)
//         };
//     }

//     // Validation de la longueur maximale
//     if (rules.maxLength && value.length > rules.maxLength) {
//         return {
//             isValid: false,
//             message: ValidationMessages.maxLength.replace('{max}', rules.maxLength)
//         };
//     }

//     // Validation par pattern regex
//     if (rules.pattern) {
//         const pattern = typeof rules.pattern === 'string' ? ValidationPatterns[rules.pattern] : rules.pattern;
//         if (pattern && !pattern.test(value)) {
//             return {
//                 isValid: false,
//                 message: rules.message || ValidationMessages[rules.pattern] || 'Format invalide'
//             };
//         }
//     }

//     // Validation de correspondance avec un autre champ
//     if (rules.match) {
//         const matchValue = typeof rules.match === 'function' ? rules.match() : rules.match;
//         if (value !== matchValue) {
//             return {
//                 isValid: false,
//                 message: rules.matchMessage || ValidationMessages.match
//             };
//         }
//     }

//     // Validation personnalisée
//     if (rules.custom && typeof rules.custom === 'function') {
//         const customResult = rules.custom(value);
//         if (customResult !== true) {
//             return {
//                 isValid: false,
//                 message: typeof customResult === 'string' ? customResult : 'Validation échouée'
//             };
//         }
//     }

//     return {
//         isValid: true,
//         message: ''
//     };
// }

// /**
//  * Affiche un message d'erreur pour un champ spécifique
//  * @param {string} fieldId - ID du champ
//  * @param {string} message - Message d'erreur
//  */
// export function showFieldError(fieldId, message) {
//     const field = document.getElementById(fieldId);
//     if (!field) return;

//     // Supprimer les anciennes erreurs
//     clearFieldError(fieldId);

//     // Ajouter la classe d'erreur au champ
//     field.classList.add('input-error');
//     field.classList.remove('input-success');

//     // Créer et ajouter le message d'erreur
//     const errorElement = document.createElement('div');
//     errorElement.className = 'label';
//     errorElement.innerHTML = `<span class="label-text-alt text-error">${message}</span>`;
//     errorElement.setAttribute('data-error-for', fieldId);

//     // Insérer après le champ ou son conteneur parent
//     const container = field.closest('.form-control') || field.parentElement;
//     container.appendChild(errorElement);
// }

// /**
//  * Affiche un message de succès pour un champ spécifique
//  * @param {string} fieldId - ID du champ
//  * @param {string} message - Message de succès (optionnel)
//  */
// export function showFieldSuccess(fieldId, message = '') {
//     const field = document.getElementById(fieldId);
//     if (!field) return;

//     // Supprimer les erreurs
//     clearFieldError(fieldId);

//     // Ajouter la classe de succès
//     field.classList.add('input-success');
//     field.classList.remove('input-error');

//     // Ajouter un message de succès si fourni
//     if (message) {
//         const successElement = document.createElement('div');
//         successElement.className = 'label';
//         successElement.innerHTML = `<span class="label-text-alt text-success">${message}</span>`;
//         successElement.setAttribute('data-success-for', fieldId);

//         const container = field.closest('.form-control') || field.parentElement;
//         container.appendChild(successElement);
//     }
// }

// /**
//  * Supprime les messages d'erreur et de succès d'un champ
//  * @param {string} fieldId - ID du champ
//  */
// export function clearFieldError(fieldId) {
//     const field = document.getElementById(fieldId);
//     if (!field) return;

//     // Supprimer les classes d'état
//     field.classList.remove('input-error', 'input-success');

//     // Supprimer les messages d'erreur et de succès
//     const container = field.closest('.form-control') || field.parentElement;
//     const errorElements = container.querySelectorAll(`[data-error-for="${fieldId}"], [data-success-for="${fieldId}"]`);
//     errorElements.forEach(element => element.remove());
// }

// /**
//  * Valide un champ en temps réel et affiche le résultat
//  * @param {string} fieldId - ID du champ
//  * @param {Object} rules - Règles de validation
//  * @param {boolean} showSuccess - Afficher le succès ou non
//  * @returns {boolean} - True si valide
//  */
// export function validateAndShowField(fieldId, rules = {}, showSuccess = false) {
//     const field = document.getElementById(fieldId);
//     if (!field) return false;

//     const value = field.value;
//     const result = validateField(value, rules);

//     if (result.isValid) {
//         if (showSuccess) {
//             showFieldSuccess(fieldId, rules.successMessage || '✓');
//         } else {
//             clearFieldError(fieldId);
//         }
//         return true;
//     } else {
//         showFieldError(fieldId, result.message);
//         return false;
//     }
// }

// /**
//  * Valide un formulaire complet
//  * @param {Object} formConfig - Configuration du formulaire
//  * @returns {Object} - Résultat de la validation {isValid: boolean, errors: Object}
//  */
// export function validateForm(formConfig) {
//     const errors = {};
//     let isValid = true;

//     for (const [fieldId, rules] of Object.entries(formConfig)) {
//         const field = document.getElementById(fieldId);
//         if (!field) continue;

//         const result = validateField(field.value, rules);
//         if (!result.isValid) {
//             errors[fieldId] = result.message;
//             isValid = false;
//             showFieldError(fieldId, result.message);
//         } else {
//             clearFieldError(fieldId);
//             if (rules.showSuccess) {
//                 showFieldSuccess(fieldId, rules.successMessage || '✓');
//             }
//         }
//     }

//     return { isValid, errors };
// }

// /**
//  * Configure la validation en temps réel pour un formulaire
//  * @param {Object} formConfig - Configuration du formulaire
//  * @param {Object} options - Options de configuration
//  */
// export function setupRealTimeValidation(formConfig, options = {}) {
//     const {
//         validateOnInput = true,
//         validateOnBlur = true,
//         showSuccess = false,
//         debounceTime = 300
//     } = options;

//     for (const [fieldId, rules] of Object.entries(formConfig)) {
//         const field = document.getElementById(fieldId);
//         if (!field) continue;

//         let debounceTimer;

//         // Validation sur saisie (avec debounce)
//         if (validateOnInput) {
//             field.addEventListener('input', () => {
//                 clearTimeout(debounceTimer);
//                 debounceTimer = setTimeout(() => {
//                     validateAndShowField(fieldId, rules, showSuccess);
//                 }, debounceTime);
//             });
//         }

//         // Validation sur perte de focus
//         if (validateOnBlur) {
//             field.addEventListener('blur', () => {
//                 clearTimeout(debounceTimer);
//                 validateAndShowField(fieldId, rules, showSuccess);
//             });
//         }

//         // Nettoyer les erreurs sur focus
//         field.addEventListener('focus', () => {
//             if (field.classList.contains('input-error')) {
//                 clearFieldError(fieldId);
//             }
//         });
//     }
// }

// /**
//  * Affiche une alerte globale (succès, erreur, info, warning)
//  * @param {string} type - Type d'alerte (success, error, info, warning)
//  * @param {string} message - Message à afficher
//  * @param {Object} options - Options d'affichage
//  */
// export function showAlert(type, message, options = {}) {
//     const {
//         containerId = 'alert-container',
//         autoHide = true,
//         hideDelay = 5000,
//         position = 'top'
//     } = options;

//     // Créer le conteneur d'alertes s'il n'existe pas
//     let container = document.getElementById(containerId);
//     if (!container) {
//         container = document.createElement('div');
//         container.id = containerId;
//         container.className = `fixed ${position === 'top' ? 'top-4' : 'bottom-4'} right-4 z-50 space-y-2`;
//         document.body.appendChild(container);
//     }

//     // Créer l'alerte
//     const alertElement = document.createElement('div');
//     const alertClasses = {
//         success: 'alert-success',
//         error: 'alert-error',
//         info: 'alert-info',
//         warning: 'alert-warning'
//     };

//     const icons = {
//         success: `<svg class="stroke-current shrink-0 w-6 h-6" fill="none" viewBox="0 0 24 24">
//                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
//                   </svg>`,
//         error: `<svg class="stroke-current shrink-0 w-6 h-6" fill="none" viewBox="0 0 24 24">
//                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
//                 </svg>`,
//         info: `<svg class="stroke-current shrink-0 w-6 h-6" fill="none" viewBox="0 0 24 24">
//                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
//                </svg>`,
//         warning: `<svg class="stroke-current shrink-0 w-6 h-6" fill="none" viewBox="0 0 24 24">
//                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
//                   </svg>`
//     };

//     alertElement.className = `alert ${alertClasses[type]} shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
//     alertElement.innerHTML = `
//         ${icons[type]}
//         <span>${message}</span>
//         <button class="btn btn-sm btn-ghost" onclick="this.parentElement.remove()">
//             <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
//             </svg>
//         </button>
//     `;

//     container.appendChild(alertElement);

//     // Animation d'entrée
//     setTimeout(() => {
//         alertElement.classList.remove('translate-x-full', 'opacity-0');
//     }, 10);

//     // Auto-suppression
//     if (autoHide) {
//         setTimeout(() => {
//             alertElement.classList.add('translate-x-full', 'opacity-0');
//             setTimeout(() => {
//                 if (alertElement.parentElement) {
//                     alertElement.remove();
//                 }
//             }, 300);
//         }, hideDelay);
//     }

//     return alertElement;
// }

// /**
//  * Supprime toutes les alertes actives
//  * @param {string} containerId - ID du conteneur d'alertes
//  */
// export function clearAllAlerts(containerId = 'alert-container') {
//     const container = document.getElementById(containerId);
//     if (container) {
//         container.innerHTML = '';
//     }
// }

// /**
//  * Ajoute des styles CSS pour les états de validation
//  */
// export function addValidationStyles() {
//     const styleId = 'form-validation-styles';
//     if (document.getElementById(styleId)) return;

//     const style = document.createElement('style');
//     style.id = styleId;
//     style.textContent = `
//         .input-error {
//             border-color: hsl(var(--er)) !important;
//             background-color: hsl(var(--er) / 0.1);
//         }

//         .input-success {
//             border-color: hsl(var(--su)) !important;
//             background-color: hsl(var(--su) / 0.1);
//         }

//         .input-error:focus {
//             outline-color: hsl(var(--er)) !important;
//             border-color: hsl(var(--er)) !important;
//         }

//         .input-success:focus {
//             outline-color: hsl(var(--su)) !important;
//             border-color: hsl(var(--su)) !important;
//         }

//         .form-control .label-text-alt.text-error {
//             color: hsl(var(--er));
//             font-size: 0.75rem;
//             margin-top: 0.25rem;
//         }

//         .form-control .label-text-alt.text-success {
//             color: hsl(var(--su));
//             font-size: 0.75rem;
//             margin-top: 0.25rem;
//         }
//     `;
//     document.head.appendChild(style);
// }

// /**
//  * Initialise le service de validation (à appeler au chargement de la page)
//  */
// export function initFormValidation() {
//     addValidationStyles();
// }

// /**
//  * Configurations prédéfinies pour des types de formulaires courants
//  */
// export const FormConfigs = {
//     login: {
//         email: {
//             required: true,
//             pattern: 'email',
//             successMessage: '✓'
//         },
//         password: {
//             required: true,
//             pattern: 'passwordSimple',
//             successMessage: '✓'
//         }
//     },

//     register: {
//         firstName: {
//             required: true,
//             pattern: 'name',
//             successMessage: '✓'
//         },
//         lastName: {
//             required: true,
//             pattern: 'name',
//             successMessage: '✓'
//         },
//         email: {
//             required: true,
//             pattern: 'email',
//             successMessage: '✓'
//         },
//         password: {
//             required: true,
//             pattern: 'password',
//             successMessage: '✓'
//         },
//         confirmPassword: {
//             required: true,
//             match: () => document.getElementById('password')?.value,
//             matchMessage: 'Les mots de passe ne correspondent pas',
//             successMessage: '✓'
//         }
//     },

//     contact: {
//         name: {
//             required: true,
//             pattern: 'name',
//             successMessage: '✓'
//         },
//         email: {
//             required: true,
//             pattern: 'email',
//             successMessage: '✓'
//         },
//         phone: {
//             required: false,
//             pattern: 'phone',
//             successMessage: '✓'
//         },
//         message: {
//             required: true,
//             minLength: 10,
//             maxLength: 1000,
//             successMessage: '✓'
//         }
//     }
// };

// // Auto-initialisation si le DOM est déjà chargé
// if (document.readyState === 'loading') {
//     document.addEventListener('DOMContentLoaded', initFormValidation);
// } else {
//     initFormValidation();
// }