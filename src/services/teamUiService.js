/**
 * Service UI pour la génération automatique des cards d'équipe avec DaisyUI
 */

import { teamData } from './teamDataService.js';

/**
 * Génère le HTML d'une card de membre d'équipe
 * @param {Object} member - Données du membre
 * @param {number} index - Index pour l'animation
 * @returns {string} HTML de la card
 */
function createTeamMemberCard(member, index) {
    return `
        <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 animate-fade-in group"
             style="animation-delay: ${index * 100}ms">
            <!-- Image du membre -->
            <figure class="px-6 pt-6">
                <div class="avatar">
                    <div class="w-24 h-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 group-hover:ring-secondary transition-all duration-300">
                        <img src="${member.image}"
                             alt="Photo de ${member.name}"
                             class="rounded-full object-cover"
                             loading="lazy"
                             onerror="this.src='https://img.daisyui.com/images/stock/photo-1534528741775-53994a69daeb.webp'" />
                    </div>
                </div>
            </figure>

            <!-- Contenu de la card -->
            <div class="card-body items-center text-center px-6 pb-6">
                <!-- Nom et rôle -->
                <h2 class="card-title text-lg font-bold text-base-content group-hover:text-primary transition-colors duration-300">
                    ${member.name}
                </h2>

                <!-- Badge du rôle -->
                <div class="badge badge-secondary badge-outline mb-3">
                    ${member.role}
                </div>

                <!-- Bio -->
                <p class="text-sm text-base-content/70 mb-3 line-clamp-2">
                    ${member.bio}
                </p>

                <!-- Compétences (si disponibles) -->
                ${member.skills ? `
                    <div class="flex flex-wrap gap-1 mb-3 justify-center">
                        ${member.skills.slice(0, 3).map(skill => `
                            <span class="badge badge-outline badge-xs">${skill}</span>
                        `).join('')}
                        ${member.skills.length > 3 ? `<span class="badge badge-outline badge-xs">+${member.skills.length - 3}</span>` : ''}
                    </div>
                ` : ''}

                <!-- Expérience et localisation -->
                ${member.experience || member.location ? `
                    <div class="text-xs text-base-content/60 mb-3 space-y-1">
                        ${member.experience ? `<div>📅 ${member.experience} d'expérience</div>` : ''}
                        ${member.location ? `<div>📍 ${member.location}</div>` : ''}
                    </div>
                ` : ''}

                <!-- Actions -->
                <div class="card-actions justify-center w-full">
                    <!-- Bouton email -->
                    <a href="mailto:${member.email}"
                       class="btn btn-primary btn-sm flex-1 max-w-32 group-hover:btn-secondary transition-all duration-300">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Contact
                    </a>

                    <!-- Bouton profil -->
                    <button class="btn btn-outline btn-sm flex-1 max-w-32"
                            onclick="showMemberDetails('${member.name}')">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Profil
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Génère le HTML pour tous les membres de l'équipe
 * @param {Array} members - Tableau des membres
 * @returns {string} HTML complet
 */
function generateTeamGrid(members) {
    const cardsHTML = members.map((member, index) => createTeamMemberCard(member, index)).join('');

    return `
        <!-- Grille responsive des membres -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            ${cardsHTML}
        </div>

        <!-- Statistiques de l'équipe -->
        <div class="stats stats-vertical lg:stats-horizontal shadow w-full mb-8">
            <div class="stat">
                <div class="stat-figure text-primary">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="stat-title">Membres de l'équipe</div>
                <div class="stat-value text-primary">${members.length}</div>
                <div class="stat-desc">Professionnels talentueux</div>
            </div>

            <div class="stat">
                <div class="stat-figure text-secondary">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <div class="stat-title">Spécialités</div>
                <div class="stat-value text-secondary">${new Set(members.map(m => m.role.split(' ')[0])).size}</div>
                <div class="stat-desc">Domaines d'expertise</div>
            </div>

            <div class="stat">
                <div class="stat-figure text-accent">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="stat-title">Expérience</div>
                <div class="stat-value text-accent">5+</div>
                <div class="stat-desc">Années d'expertise</div>
            </div>
        </div>
    `;
}

/**
 * Affiche les détails d'un membre dans une modal
 * @param {string} memberName - Nom du membre
 */
function showMemberDetails(memberName) {
    const member = teamData.find(m => m.name === memberName);
    if (!member) return;

    // Créer la modal si elle n'existe pas
    let modal = document.getElementById('member-details-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'member-details-modal';
        modal.className = 'modal';
        document.body.appendChild(modal);
    }

    modal.innerHTML = `
        <div class="modal-box w-11/12 max-w-2xl">
            <!-- Header -->
            <div class="flex items-center mb-6">
                <div class="avatar mr-4">
                    <div class="w-20 h-20 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                        <img src="${member.image}" alt="Photo de ${member.name}" class="rounded-full object-cover" />
                    </div>
                </div>
                <div>
                    <h3 class="font-bold text-2xl text-base-content">${member.name}</h3>
                    <div class="badge badge-secondary badge-lg mt-2">${member.role}</div>
                </div>
            </div>

            <!-- Contenu -->
            <div class="space-y-6">
                <!-- À propos -->
                <div>
                    <h4 class="font-semibold text-lg mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        À propos
                    </h4>
                    <p class="text-base-content/80">${member.bio}</p>
                </div>

                <!-- Compétences -->
                ${member.skills ? `
                    <div>
                        <h4 class="font-semibold text-lg mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Compétences
                        </h4>
                        <div class="flex flex-wrap gap-2">
                            ${member.skills.map(skill => `
                                <span class="badge badge-primary badge-lg">${skill}</span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <!-- Informations -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${member.experience ? `
                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-title text-sm">Expérience</div>
                            <div class="stat-value text-lg text-primary">${member.experience}</div>
                        </div>
                    ` : ''}

                    ${member.location ? `
                        <div class="stat bg-base-200 rounded-lg p-4">
                            <div class="stat-title text-sm">Localisation</div>
                            <div class="stat-value text-lg text-secondary">${member.location}</div>
                        </div>
                    ` : ''}
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="font-semibold text-lg mb-2 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Contact
                    </h4>
                    <div class="flex items-center space-x-2">
                        <a href="mailto:${member.email}" class="link link-primary">${member.email}</a>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-2 pt-4">
                    <a href="mailto:${member.email}" class="btn btn-primary flex-1">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Envoyer un email
                    </a>
                    <button class="btn btn-outline flex-1" onclick="navigator.share && navigator.share({title: 'Profil de ${member.name}', text: '${member.bio}', url: window.location.href})">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        Partager
                    </button>
                </div>
            </div>

            <!-- Bouton fermer -->
            <div class="modal-action">
                <button class="btn" onclick="document.getElementById('member-details-modal').close()">Fermer</button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button>close</button>
        </form>
    `;

    // Ouvrir la modal
    modal.showModal();
}

/**
 * Rend l'équipe dans le conteneur spécifié
 * @param {string} containerId - ID du conteneur
 * @param {Array} members - Tableau des membres (optionnel, utilise teamData par défaut)
 */
export function renderTeam(containerId = 'team-container', members = teamData) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Conteneur ${containerId} non trouvé`);
        return;
    }

    // Générer et injecter le HTML
    container.innerHTML = generateTeamGrid(members);

    // Ajouter les animations d'entrée
    setTimeout(() => {
        const cards = container.querySelectorAll('.animate-fade-in');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 100);
}

/**
 * Filtre les membres par rôle
 * @param {string} role - Rôle à filtrer
 * @param {string} containerId - ID du conteneur
 */
export function filterTeamByRole(role, containerId = 'team-container') {
    const filteredMembers = role === 'all'
        ? teamData
        : teamData.filter(member => member.role.toLowerCase().includes(role.toLowerCase()));

    renderTeam(containerId, filteredMembers);
}

/**
 * Recherche des membres par nom ou rôle
 * @param {string} query - Terme de recherche
 * @param {string} containerId - ID du conteneur
 */
export function searchTeamMembers(query, containerId = 'team-container') {
    const filteredMembers = teamData.filter(member =>
        member.name.toLowerCase().includes(query.toLowerCase()) ||
        member.role.toLowerCase().includes(query.toLowerCase()) ||
        member.bio.toLowerCase().includes(query.toLowerCase())
    );

    renderTeam(containerId, filteredMembers);
}

/**
 * Ajoute les filtres et la recherche à la page
 * @param {string} containerId - ID du conteneur où ajouter les contrôles
 */
export function addTeamControls(containerId = 'team-container') {
    const container = document.getElementById(containerId);
    if (!container) return;

    const controlsHTML = `
        <div class="mb-8 space-y-4">
            <!-- Barre de recherche -->
            <div class="form-control w-full max-w-md mx-auto">
                <div class="input-group">
                    <input type="text"
                           placeholder="Rechercher un membre..."
                           class="input input-bordered flex-1"
                           id="team-search" />
                    <button class="btn btn-square btn-primary" onclick="searchTeam()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Filtres par rôle -->
            <div class="flex flex-wrap justify-center gap-2">
                <button class="btn btn-sm btn-primary" onclick="filterTeam('all')">Tous</button>
                <button class="btn btn-sm btn-outline" onclick="filterTeam('développeur')">Développeurs</button>
                <button class="btn btn-sm btn-outline" onclick="filterTeam('designer')">Designers</button>
                <button class="btn btn-sm btn-outline" onclick="filterTeam('manager')">Managers</button>
                <button class="btn btn-sm btn-outline" onclick="filterTeam('devops')">DevOps</button>
            </div>
        </div>
        <div id="team-grid"></div>
    `;

    container.innerHTML = controlsHTML;
}

// Fonctions globales pour les événements onclick
window.showMemberDetails = showMemberDetails;
window.filterTeam = (role) => filterTeamByRole(role, 'team-grid');
window.searchTeam = () => {
    const query = document.getElementById('team-search').value;
    searchTeamMembers(query, 'team-grid');
};

// Export des données pour utilisation externe
export { teamData };