//---------------------------------------------------------------------------
//----------------------- G<PERSON><PERSON>ÉRATION AUTOMATIQUE DES CARDS ÉQUIPE ----------------
//---------------------------------------------------------------------------

// Données de l'équipe sous forme de tableau d'objets
// Fonction pour créer une carte d'équipe
function createTeamCard(member) {
    return `
    <img
        <div class="card bg-base-100 w-96 shadow-sm">
            <figure>
                <img src="${member.image}" alt="${member.name}" />
            </figure>
            <div class="card-body">
                <h2 class="card-title">${member.name}</h2>
                <p><strong>Rôle:</strong> ${member.role}</p>
                <p>${member.bio}</p>
                <div class="card-actions justify-end">
                    <a href="mailto:${member.email}" class="btn btn-primary">Contacter</a>
                </div>
            </div>
        </div>
    `;
}

const cardContainer = document.getElementById('team-container');
function createTeamCardProper(member){
const card = document.createElement('div');
card.classList.add('card', 'bg-base-100', 'w-96', 'shadow-sm');
const figure = document.createElement('figure');
const img = document.createElement('img');
img.src = member.image;
img.alt = member.name;
figure.appendChild(img);
card.appendChild(figure);
const cardBody = document.createElement('div');
cardBody.classList.add('card-body');
const cardTitle = document.createElement('h2');
cardTitle.classList.add('card-title');
cardTitle.textContent = member.name;
cardBody.appendChild(cardTitle);
const cardRole = document.createElement('p');
cardRole.innerHTML = `<strong>Rôle:</strong> ${member.role}`;
cardBody.appendChild(cardRole);
const cardBio = document.createElement('p');
cardBio.textContent = member.bio;
cardBody.appendChild(cardBio);
const cardActions = document.createElement('div');
cardActions.classList.add('card-actions', 'justify-end');
const cardButton = document.createElement('a');
cardButton.href = `mailto:${member.email}`;
cardButton.classList.add('btn', 'btn-primary');
cardButton.textContent = 'Contacter';
cardActions.appendChild(cardButton);
cardBody.appendChild(cardActions);
card.appendChild(cardBody);

return card;
}

// Générer et ajouter les cartes au conteneur
if (cardContainer) {
teamData.forEach(member => {
    cardContainer.innerHTML += DOMPurify.sanitize( createTeamCard(member));
    // cardContainer.appendChild(createTeamCardProper(member));
    // cardContainer.innerHTML +=(createTeamCardProper(member));
});
}