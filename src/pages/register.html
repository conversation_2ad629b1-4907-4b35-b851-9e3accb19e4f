<!DOCTYPE html>
<html lang="fr" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Super Cours JS</title>
    <link rel="stylesheet" href="/src/style.css">
</head>
<body class="min-h-screen bg-base-200">
    <div class="min-h-screen flex flex-col">
        <!-- Navigation -->
        <nav class="navbar bg-base-100 shadow-lg">
            <div class="navbar-start">
                <a href="/" class="btn btn-ghost text-xl">🚀 Super Cours JS</a>
            </div>
            <div class="navbar-end">
                <a href="/src/pages/login.html" class="btn btn-outline btn-primary">Se connecter</a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 flex items-center justify-center px-6 py-12">
            <div class="w-full max-w-md">
                <!-- Card d'inscription -->
                <div class="card bg-base-100 shadow-2xl border border-base-300">
                    <div class="card-body">
                        <!-- Header -->
                        <div class="text-center mb-6">
                            <div class="avatar placeholder mb-4">
                                <div class="bg-secondary text-secondary-content rounded-full w-16">
                                    <span class="text-2xl">👤</span>
                                </div>
                            </div>
                            <h1 class="text-3xl font-bold text-base-content">Inscription</h1>
                            <p class="text-base-content/70 mt-2">Créez votre compte gratuitement</p>
                        </div>

                        <!-- Formulaire -->
                        <form class="space-y-4" id="registerForm">
                            <!-- Prénom -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Prénom</span>
                                </label>
                                <input 
                                    type="text" 
                                    placeholder="Jean" 
                                    class="input input-bordered w-full focus:input-primary" 
                                    id="firstName"
                                    required
                                />
                            </div>

                            <!-- Nom -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Nom</span>
                                </label>
                                <input 
                                    type="text" 
                                    placeholder="Dupont" 
                                    class="input input-bordered w-full focus:input-primary" 
                                    id="lastName"
                                    required
                                />
                            </div>

                            <!-- Email -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Email</span>
                                </label>
                                <input 
                                    type="email" 
                                    placeholder="<EMAIL>" 
                                    class="input input-bordered w-full focus:input-primary" 
                                    id="email"
                                    required
                                />
                            </div>

                            <!-- Mot de passe -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Mot de passe</span>
                                </label>
                                <div class="relative">
                                    <input 
                                        type="password" 
                                        placeholder="••••••••" 
                                        class="input input-bordered w-full pr-10 focus:input-primary" 
                                        id="password"
                                        required
                                    />
                                    <button 
                                        type="button" 
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
                                        id="togglePassword"
                                    >
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="eyeIcon">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <label class="label">
                                    <span class="label-text-alt text-base-content/60">
                                        8 caractères min, avec majuscule, minuscule, chiffre et caractère spécial
                                    </span>
                                </label>
                            </div>

                            <!-- Confirmation mot de passe -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Confirmer le mot de passe</span>
                                </label>
                                <input 
                                    type="password" 
                                    placeholder="••••••••" 
                                    class="input input-bordered w-full focus:input-primary" 
                                    id="confirmPassword"
                                    required
                                />
                            </div>

                            <!-- Conditions d'utilisation -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" class="checkbox checkbox-primary checkbox-sm mr-3" id="acceptTerms" required />
                                    <span class="label-text">
                                        J'accepte les 
                                        <a href="#" class="link link-primary">conditions d'utilisation</a> 
                                        et la 
                                        <a href="#" class="link link-primary">politique de confidentialité</a>
                                    </span>
                                </label>
                            </div>

                            <!-- Newsletter -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start">
                                    <input type="checkbox" class="checkbox checkbox-secondary checkbox-sm mr-3" id="newsletter" />
                                    <span class="label-text">Recevoir la newsletter (optionnel)</span>
                                </label>
                            </div>

                            <!-- Bouton d'inscription -->
                            <div class="form-control mt-6">
                                <button type="submit" class="btn btn-secondary btn-block">
                                    <span class="loading loading-spinner loading-sm hidden" id="registerSpinner"></span>
                                    <span id="registerText">Créer mon compte</span>
                                </button>
                            </div>
                        </form>

                        <!-- Divider -->
                        <div class="divider">ou</div>

                        <!-- Connexion sociale -->
                        <div class="space-y-3">
                            <button class="btn btn-outline btn-block">
                                <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                S'inscrire avec Google
                            </button>
                            
                            <button class="btn btn-outline btn-block">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                                S'inscrire avec Facebook
                            </button>
                        </div>

                        <!-- Lien de connexion -->
                        <div class="text-center mt-6">
                            <p class="text-base-content/70">
                                Déjà un compte ? 
                                <a href="/src/pages/login.html" class="link link-primary font-medium">Se connecter</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer footer-center p-4 bg-base-300 text-base-content">
            <div id="footer-container"></div>
        </footer>
    </div>

    <script type="module" src="/src/main.js"></script>
    
    <script type="module">
        import { 
            setupRealTimeValidation, 
            validateForm, 
            showAlert, 
            clearAllAlerts,
            FormConfigs 
        } from '/src/services/formValidationService.js';

        // Configuration du formulaire d'inscription
        const registerFormConfig = {
            ...FormConfigs.register,
            acceptTerms: {
                required: true,
                custom: (value) => {
                    const checkbox = document.getElementById('acceptTerms');
                    return checkbox.checked || 'Vous devez accepter les conditions d\'utilisation';
                }
            }
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Configuration de la validation en temps réel
            setupRealTimeValidation(registerFormConfig, {
                validateOnInput: true,
                validateOnBlur: true,
                showSuccess: true,
                debounceTime: 500
            });

            // Animation d'entrée
            const card = document.querySelector('.card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });

        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                `;
            }
        });

        // Form submission
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            clearAllAlerts();
            
            const validationResult = validateForm(registerFormConfig);
            
            if (!validationResult.isValid) {
                showAlert('error', 'Veuillez corriger les erreurs dans le formulaire');
                return;
            }
            
            // Show loading state
            const spinner = document.getElementById('registerSpinner');
            const registerText = document.getElementById('registerText');
            const submitBtn = e.target.querySelector('button[type="submit"]');
            
            spinner.classList.remove('hidden');
            registerText.textContent = 'Création du compte...';
            submitBtn.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                spinner.classList.add('hidden');
                registerText.textContent = 'Créer mon compte';
                submitBtn.disabled = false;
                
                showAlert('success', 'Compte créé avec succès ! Redirection vers la connexion...', {
                    autoHide: false
                });
                
                setTimeout(() => {
                    window.location.href = '/src/pages/login.html';
                }, 2000);
            }, 2000);
        });
    </script>
</body>
</html>
