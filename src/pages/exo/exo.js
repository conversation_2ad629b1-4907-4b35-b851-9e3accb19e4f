console.log('Exo.js');

/**
 * Configuration des exercices disponibles
 * Chaque exercice contient les informations nécessaires pour générer sa carte
 */
const exercicesData = [
    {
        id: 'dom-events',
        title: 'Exercices DOM Events',
        description: 'Pratiquez la gestion des événements DOM avec des exercices interactifs sur les clics, saisie clavier et formulaires.',
        difficulty: 'Débutant',
        duration: '30 min',
        topics: ['DOM', 'Événements', 'Interactions'],
        file: 'dom-events.html',
        status: 'available',
        badge: 'Interactif',
        category: 'DOM',
        order: 1,
        exerciseType: 'pratique'
    },
    {
        id: 'variables',
        title: 'Exercices Variables',
        description: 'Exercices pratiques sur la déclaration, l\'affectation et la manipulation des variables JavaScript.',
        difficulty: 'Débutant',
        duration: '25 min',
        topics: ['Variables', 'Types', 'Portée'],
        file: 'variables.html',
        status: 'available',
        badge: 'Fondamental',
        category: 'Fondamentaux',
        order: 2,
        exerciseType: 'quiz'
    },
    {
        id: 'dom-selectors',
        title: 'Exercices Sélecteurs DOM',
        description: 'Maîtrisez les sélecteurs DOM avec des exercices pratiques sur querySelector, getElementById et plus.',
        difficulty: 'Débutant',
        duration: '35 min',
        topics: ['DOM', 'Sélecteurs', 'Manipulation'],
        file: 'dom-selectors.html',
        status: 'available',
        badge: null,
        category: 'DOM',
        order: 3,
        exerciseType: 'pratique'
    },
    {
        id: 'mensonge',
        title: 'Compteur de Mensonges',
        description: 'Exercice ludique : créez un compteur de mensonges avec localStorage et gestion d\'état.',
        difficulty: 'Intermédiaire',
        duration: '45 min',
        topics: ['LocalStorage', 'Compteur', 'État'],
        file: 'mensonge.html',
        status: 'available',
        badge: 'Amusant',
        category: 'Projets',
        order: 4,
        exerciseType: 'projet'
    },
    {
        id: 'editeur-texte',
        title: 'Éditeur de Texte',
        description: 'Créez un éditeur de texte simple avec sauvegarde automatique et fonctionnalités avancées.',
        difficulty: 'Intermédiaire',
        duration: '60 min',
        topics: ['DOM', 'LocalStorage', 'Éditeur'],
        file: 'editeur-texte.html',
        status: 'available',
        badge: 'Nouveau',
        category: 'Projets',
        order: 5,
        exerciseType: 'projet'
    }
];

/**
 * Génère une carte exercice avec DaisyUI
 * @param {Object} exercice - Les données de l'exercice
 * @returns {HTMLElement} L'élément carte généré
 */
function createExerciceCard(exercice) {
    const card = document.createElement('div');
    card.className = 'card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-300';

    // Couleurs selon la difficulté
    const difficultyColors = {
        'Débutant': 'badge-success',
        'Intermédiaire': 'badge-warning',
        'Avancé': 'badge-error'
    };

    // Couleurs selon la catégorie
    const categoryColors = {
        'Fondamentaux': 'badge-primary',
        'DOM': 'badge-secondary',
        'Projets': 'badge-accent',
        'Algorithmes': 'badge-info'
    };

    // Icônes selon le type d'exercice
    const exerciseTypeIcons = {
        'pratique': '🛠️',
        'quiz': '❓',
        'projet': '🚀',
        'challenge': '⚡'
    };

    // Couleurs selon le statut
    const statusConfig = {
        'available': {
            buttonClass: 'btn-primary',
            buttonText: 'Commencer l\'exercice',
            disabled: false
        },
        'coming-soon': {
            buttonClass: 'btn-disabled',
            buttonText: 'Bientôt disponible',
            disabled: true
        },
        'completed': {
            buttonClass: 'btn-success',
            buttonText: 'Refaire l\'exercice',
            disabled: false
        }
    };

    const config = statusConfig[exercice.status] || statusConfig['available'];

    card.innerHTML = `
        <div class="card-body">
            <div class="flex justify-between items-start mb-2">
                <div class="flex items-center gap-2">
                    <span class="text-lg">${exerciseTypeIcons[exercice.exerciseType] || '📝'}</span>
                    <span class="text-sm font-mono text-base-content/60">#${exercice.order}</span>
                    <h2 class="card-title text-lg font-bold">${exercice.title}</h2>
                </div>
                <div class="flex gap-1">
                    ${exercice.badge ? `<div class="badge badge-secondary badge-sm">${exercice.badge}</div>` : ''}
                    <div class="badge ${categoryColors[exercice.category]} badge-sm">${exercice.category}</div>
                </div>
            </div>

            <p class="text-sm text-base-content/70 mb-4">${exercice.description}</p>

            <div class="flex flex-wrap gap-2 mb-4">
                ${exercice.topics.map(topic => `<span class="badge badge-outline badge-sm">${topic}</span>`).join('')}
            </div>

            <div class="grid grid-cols-3 gap-4 mb-4 text-sm">
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${exercice.duration}</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="badge ${difficultyColors[exercice.difficulty]} badge-sm">${exercice.difficulty}</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-xs text-base-content/60">${exercice.exerciseType}</span>
                </div>
            </div>

            <div class="card-actions justify-end">
                <button class="btn ${config.buttonClass} btn-sm"
                        ${config.disabled ? 'disabled' : ''}
                        onclick="openExercice('${exercice.file}', '${exercice.id}')">
                    ${config.buttonText}
                </button>
            </div>
        </div>
    `;

    return card;
}

//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS--------------------------------
//---------------------------------------------------------------------------

// const btnClicMeElement = document.querySelector('#btn-clic-me');
// const inputTextElement = document.querySelector('#input-text');
// const renderKeyElement = document.querySelector('#renderKey');
// inputTextElement.addEventListener('keyup', (eventDuclavier) => {
//     console.log(eventDuclavier);
//     console.log('eventDuclavier.key', eventDuclavier.key);
//     // ok mais bof car car interprète aussi les tab backspace etc...
//     // renderKeyElement.textContent += eventDuclavier.key; 
//     renderKeyElement.textContent += eventDuclavier.target.value;
//     // if(inputTextElement.value.length >= 5){
//     //     btnClicMeElement.disabled = true;
//     // }else{
//     //     btnClicMeElement.disabled = false;
//     // }
//     // Ou version optimisée avec condition ternaire
//     btnClicMeElement.disabled = inputTextElement.value.length >= 5 ? true : false;
// });

// const monTextArea = document.querySelector('#formMessage');
// const monBtn = document.querySelector('#formSubmitBtn');
// console.log(monTextArea);
// console.log(monBtn);

// monTextArea.addEventListener('keyup',(event)=>{
//     // console.log(event);
//     // ? Mode cond ternaires
//     monBtn.disabled = monTextArea.value.length>=5 ? true : false;
//     // ? Mode IF classique 
//     // if(monTextArea.value.length>=5){
//     //    monBtn.disabled = true;
//     // }
//     // else{
//     //     monBtn.disabled = false
//     // }
// });

// ? Code existant pour la gestion dynamique (à conserver pour compatibilité)
// const siteNameNavbarElement = document.getElementById('site-name-navbar');
// console.log(siteNameNavbarElement);
// //!Paranoïa : on vérifie si l'élément est bien sélectionné
// if (siteNameNavbarElement) {
//   siteNameNavbarElement.textContent = siteName;
// }

// ? Exercice : Rendre Dynamique la date (année) du copyright
// const copyrightElement = document.getElementById('copyright-year');
// //!Paranoïa : on vérifie si l'élément est bien sélectionné
// if (copyrightElement) {
//   const currentYear = new Date().getFullYear();
//   copyrightElement.textContent = `Copyright © ${currentYear} - Tous droits réservés par ${siteName}`;
// }


//! Exercice : DOM Events (on click sur le main title cela modifie son texte)
// const mainTitleElement = document.getElementById('main-title');
// console.log(mainTitleElement);
// mainTitleElement.addEventListener('click', () => {
//   mainTitleElement.textContent = 'Trop un truc De Botch le JS 🫠';
// });
//! Exercice : DOM Events (on click sur le main title cela modifie son texte) version avec Booleen
// let isClicked = false;
// mainTitleElement.addEventListener('click', () => {
//     mainTitleElement.textContent = isClicked ? 'Trop un truc De Botch le JS 🫠' : 'Les DOM Events en JavaScript';
//     isClicked = !isClicked;
// })
//---------------------------------------------------------------------------
//----------------------- EXERCICES DOM EVENTS Local Storage ----------------
//---------------------------------------------------------------------------
// const inputDomElement  = document.querySelector('#input-text');
// const renderKeyElementDom = document.querySelector('#renderKey');

// inputDomElement.value = localStorage.getItem('monSuperTexte');
// renderKeyElementDom.textContent = localStorage.getItem('monSuperTexte');

//  inputDomElement.addEventListener('keyup', (event) => {
//     console.log(event);
    
//    //  renderKeyElementDom.textContent += event.target.value;
//        renderKeyElementDom.textContent = inputDomElement.value;

//     localStorage.setItem('monSuperTexte', event.target.value);
//     renderKeyElementDom.innerText = localStorage.getItem('monSuperTexte');
//  });

//  // Sélectionne l'élément <input> dans le DOM avec l'id "input-text"
// const inputDomElement = document.querySelector('#input-text');

// // Sélectionne l'élément qui affichera la valeur saisie, avec l'id "renderKey"
// const renderKeyElementDom = document.querySelector('#renderKey');

// // Récupère la valeur stockée dans le localStorage (clé "monSuperTexte")
// // et l'affiche directement dans le champ input si elle existe
// inputDomElement.value = localStorage.getItem('monSuperTexte');

// // Met également cette valeur dans le texte de l'élément affichage
// renderKeyElementDom.textContent = localStorage.getItem('monSuperTexte');

// // Ajoute un écouteur d'événement sur le champ input
// // "keyup" = déclenché à chaque relâchement de touche
// inputDomElement.addEventListener('keyup', (event) => {
//     // Affiche l'objet de l'événement dans la console (debug)
//     console.log(event);

//     // ⚠️ Ici, on ajoute (concatène) le texte au lieu de le remplacer
//     // Ce n'est probablement pas le comportement attendu
//    //  renderKeyElementDom.textContent += event.target.value;
//     renderKeyElementDom.textContent = inputDomElement.value;

//     // Enregistre la valeur actuelle du champ input dans le localStorage
//     // pour la retrouver même après rechargement de la page
//     localStorage.setItem('monSuperTexte', event.target.value);

//     // Met à jour le texte affiché avec la valeur stockée
//     // (mais cela écrase le précédent textContent)
//     renderKeyElementDom.innerText = localStorage.getItem('monSuperTexte');
// });




//---------------------------------------------------------------------------
//----------------------- SYSTÈME AUTOMATISÉ D'EXERCICES ------------------
//---------------------------------------------------------------------------

/**
 * Fonction pour ouvrir un exercice
 * @param {string} filename - Le nom du fichier exercice
 * @param {string} exerciceId - L'ID de l'exercice
 */
function openExercice(filename, exerciceId) {
    console.log(`Ouverture de l'exercice: ${exerciceId} (${filename})`);

    // Rediriger vers l'exercice
    window.location.href = `/src/pages/exo/${filename}`;

    // Optionnel: Sauvegarder le progrès dans localStorage
    const progress = JSON.parse(localStorage.getItem('exercices-progress') || '{}');
    progress[exerciceId] = {
        lastAccessed: new Date().toISOString(),
        status: 'started'
    };
    localStorage.setItem('exercices-progress', JSON.stringify(progress));
}

/**
 * Filtre les exercices selon différents critères
 * @param {string} filterType - Type de filtre ('all', 'difficulty', 'category', 'exerciseType', 'status')
 * @param {string} filterValue - Valeur du filtre
 */
function filterExercices(filterType, filterValue) {
    let filteredData = exercicesData;

    if (filterType === 'difficulty' && filterValue !== 'all') {
        filteredData = exercicesData.filter(exercice => exercice.difficulty === filterValue);
    } else if (filterType === 'category' && filterValue !== 'all') {
        filteredData = exercicesData.filter(exercice => exercice.category === filterValue);
    } else if (filterType === 'exerciseType' && filterValue !== 'all') {
        filteredData = exercicesData.filter(exercice => exercice.exerciseType === filterValue);
    } else if (filterType === 'status' && filterValue !== 'all') {
        filteredData = exercicesData.filter(exercice => exercice.status === filterValue);
    }

    renderExercicesList(filteredData);
}

/**
 * Rend la liste des exercices dans le conteneur
 * @param {Array} data - Les données des exercices à afficher
 */
function renderExercicesList(data = exercicesData) {
    const container = document.getElementById('exercices-container');
    if (!container) {
        console.warn('Conteneur exercices non trouvé');
        return;
    }

    // Vider le conteneur
    container.innerHTML = '';

    // Trier par ordre
    const sortedData = [...data].sort((a, b) => a.order - b.order);

    // Ajouter les cartes
    sortedData.forEach(exercice => {
        const card = createExerciceCard(exercice);
        container.appendChild(card);
    });

    // Afficher un message si aucun exercice trouvé
    if (data.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <div class="text-6xl mb-4">🏋️</div>
                <h3 class="text-xl font-semibold mb-2">Aucun exercice trouvé</h3>
                <p class="text-base-content/60">Essayez de modifier vos filtres ou revenez plus tard.</p>
            </div>
        `;
    }
}

/**
 * Crée la barre de filtres pour les exercices
 * @returns {HTMLElement} L'élément de filtres
 */
function createExercicesFilterBar() {
    const filterBar = document.createElement('div');
    filterBar.className = 'bg-base-200 rounded-lg p-4 mb-6';

    // Obtenir les catégories et types uniques
    const categories = [...new Set(exercicesData.map(exercice => exercice.category))];
    const exerciseTypes = [...new Set(exercicesData.map(exercice => exercice.exerciseType))];

    filterBar.innerHTML = `
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium">Filtrer par:</span>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Catégorie</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterExercices('category', this.value)">
                    <option value="all">Toutes</option>
                    ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Type</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterExercices('exerciseType', this.value)">
                    <option value="all">Tous</option>
                    ${exerciseTypes.map(type => `<option value="${type}">${type}</option>`).join('')}
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Difficulté</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterExercices('difficulty', this.value)">
                    <option value="all">Toutes</option>
                    <option value="Débutant">Débutant</option>
                    <option value="Intermédiaire">Intermédiaire</option>
                    <option value="Avancé">Avancé</option>
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Statut</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterExercices('status', this.value)">
                    <option value="all">Tous</option>
                    <option value="available">Disponible</option>
                    <option value="coming-soon">Bientôt</option>
                    <option value="completed">Terminé</option>
                </select>
            </div>

            <div class="ml-auto">
                <div class="stats stats-horizontal shadow-sm">
                    <div class="stat py-2 px-4">
                        <div class="stat-title text-xs">Total Exercices</div>
                        <div class="stat-value text-lg">${exercicesData.length}</div>
                    </div>
                    <div class="stat py-2 px-4">
                        <div class="stat-title text-xs">Types</div>
                        <div class="stat-value text-lg">${exerciseTypes.length}</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    return filterBar;
}

/**
 * Initialise la page des exercices
 */
function initExercicesPage() {
    console.log('Initialisation de la page Exercices');

    // Trouver le conteneur principal
    const mainContainer = document.querySelector('main .container') || document.querySelector('main');
    if (!mainContainer) {
        console.error('Conteneur principal non trouvé');
        return;
    }

    // Créer la structure de la page
    const pageStructure = document.createElement('div');
    pageStructure.innerHTML = `
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-4">🏋️ Exercices JavaScript</h1>
            <p class="text-lg text-base-content/70 max-w-3xl">
                Renforcez vos compétences JavaScript avec nos exercices pratiques.
                Des quiz aux projets complets, chaque exercice est conçu pour vous faire progresser.
            </p>
        </div>

        <div id="exercices-filter-container"></div>

        <div id="exercices-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Les cartes exercices seront générées ici -->
        </div>
    `;

    // Ajouter la structure à la page
    mainContainer.appendChild(pageStructure);

    // Ajouter la barre de filtres
    const filterContainer = document.getElementById('exercices-filter-container');
    if (filterContainer) {
        filterContainer.appendChild(createExercicesFilterBar());
    }

    // Rendre la liste des exercices
    renderExercicesList();

    // Charger le progrès depuis localStorage
    loadExercicesProgress();
}

/**
 * Charge le progrès des exercices depuis localStorage
 */
function loadExercicesProgress() {
    const progress = JSON.parse(localStorage.getItem('exercices-progress') || '{}');

    // Mettre à jour le statut des exercices selon le progrès
    exercicesData.forEach(exercice => {
        if (progress[exercice.id]) {
            const exerciceProgress = progress[exercice.id];
            if (exerciceProgress.status === 'completed') {
                exercice.status = 'completed';
            }
        }
    });
}

/**
 * Fonction utilitaire pour ajouter un nouvel exercice
 * @param {Object} newExercice - Les données du nouvel exercice
 */
function addNewExercice(newExercice) {
    // Validation basique
    const requiredFields = ['id', 'title', 'description', 'difficulty', 'duration', 'topics', 'file', 'category', 'exerciseType'];
    const missingFields = requiredFields.filter(field => !newExercice[field]);

    if (missingFields.length > 0) {
        console.error('Champs manquants pour le nouvel exercice:', missingFields);
        return false;
    }

    // Vérifier que l'ID est unique
    if (exercicesData.find(exercice => exercice.id === newExercice.id)) {
        console.error('Un exercice avec cet ID existe déjà:', newExercice.id);
        return false;
    }

    // Ajouter les valeurs par défaut
    const exerciceWithDefaults = {
        status: 'available',
        badge: null,
        order: exercicesData.length + 1,
        ...newExercice
    };

    // Ajouter à la liste
    exercicesData.push(exerciceWithDefaults);

    // Re-rendre la liste
    renderExercicesList();

    console.log('Nouvel exercice ajouté:', newExercice.id);
    return true;
}

/**
 * Fonction utilitaire pour créer rapidement un nouvel exercice
 * Utilisez cette fonction dans la console pour ajouter facilement de nouveaux exercices
 *
 * Exemple d'utilisation :
 * createQuickExercice('boucles', 'Exercices Boucles', 'Pratiquer les boucles for et while', 'Débutant', '40 min', ['Boucles', 'Itération'], 'Fondamentaux', 'pratique')
 */
function createQuickExercice(id, title, description, difficulty = 'Débutant', duration = '30 min', topics = [], category = 'Fondamentaux', exerciseType = 'pratique') {
    const newExercice = {
        id: id,
        title: title,
        description: description,
        difficulty: difficulty,
        duration: duration,
        topics: topics,
        file: `${id}.html`,
        status: 'coming-soon',
        badge: 'Nouveau',
        category: category,
        exerciseType: exerciseType
    };

    return addNewExercice(newExercice);
}

// Rendre les fonctions globales pour pouvoir les utiliser dans le HTML
window.openExercice = openExercice;
window.filterExercices = filterExercices;
window.addNewExercice = addNewExercice;
window.createQuickExercice = createQuickExercice;

// Initialiser la page des exercices si on est sur la page index des exercices
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si on est sur la page index des exercices
    if (window.location.pathname.includes('/exo/') &&
        (window.location.pathname.endsWith('/') || window.location.pathname.includes('index.html'))) {
        initExercicesPage();
    }
});

// Afficher des informations utiles dans la console
console.log(`
🏋️ Système d'exercices automatisé initialisé !

📝 Exercices disponibles: ${exercicesData.length}
🏷️ Catégories: ${[...new Set(exercicesData.map(e => e.category))].join(', ')}
🎯 Types: ${[...new Set(exercicesData.map(e => e.exerciseType))].join(', ')}

🛠️ Fonctions utilitaires disponibles:
- createQuickExercice(id, title, description, difficulty, duration, topics, category, exerciseType) : Créer rapidement un nouvel exercice
- addNewExercice(exerciceObject) : Ajouter un exercice avec un objet complet
- filterExercices(type, value) : Filtrer les exercices

💡 Exemple pour ajouter un nouvel exercice:
createQuickExercice('mon-exercice', 'Mon Nouvel Exercice', 'Description de l\\'exercice', 'Débutant', '30 min', ['JavaScript'], 'Fondamentaux', 'pratique')
`);