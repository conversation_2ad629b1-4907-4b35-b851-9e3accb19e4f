# Système de Leçons Automatisé

Ce système permet de gérer automatiquement la liste des leçons JavaScript avec une interface moderne utilisant DaisyUI.

## 🚀 Fonctionnalités

- **Génération automatique** des cartes leçons avec DaisyUI
- **Filtrage** par catégorie, difficulté et statut
- **Système de badges** et indicateurs visuels
- **Ordre logique** des leçons avec numérotation
- **Sauvegarde du progrès** dans localStorage
- **Interface responsive** et moderne
- **Ajout facile** de nouvelles leçons

## 📁 Structure

```
src/pages/lessons/
├── index.html              # Page principale des leçons
├── lessons.js              # Script principal du système
├── setup.html              # Leçon: Configuration
├── variables.html          # Leçon: Variables
├── variables-env.html      # Leçon: Variables d'environnement
├── dom-selectors.html      # Leçon: Sélecteurs DOM
├── dom-events.html         # Leçon: Événements DOM
├── dom-events-new.html     # Leçon: Événements DOM avancés
├── libs.html               # Leçon: Bibliothèques
└── README.md               # Cette documentation
```

## 🏷️ Catégories disponibles

- **Fondamentaux** : Concepts de base (Variables, Setup)
- **DOM** : Manipulation du DOM (Sélecteurs, Événements)
- **Configuration** : Outils et environnement
- **Outils** : Bibliothèques et frameworks

## 🛠️ Utilisation

### Ajouter une nouvelle leçon

#### Méthode 1: Via la console (rapide)
```javascript
createQuickLesson(
    'ma-lecon',                  // ID unique
    'Ma Nouvelle Leçon',         // Titre
    'Description de la leçon',   // Description
    'Débutant',                  // Difficulté (Débutant/Intermédiaire/Avancé)
    '45 min',                    // Durée estimée
    ['JavaScript', 'Concepts'],  // Topics/sujets
    'Fondamentaux'               // Catégorie
);
```

#### Méthode 2: Modification du fichier lessons.js
Ajoutez un nouvel objet dans le tableau `lessonsData` :

```javascript
{
    id: 'ma-lecon',
    title: 'Ma Nouvelle Leçon',
    description: 'Description détaillée de la leçon',
    difficulty: 'Débutant',
    duration: '45 min',
    topics: ['JavaScript', 'Concepts'],
    file: 'ma-lecon.html',
    status: 'available',
    badge: 'Nouveau',
    category: 'Fondamentaux',
    order: 8
}
```

### Statuts disponibles

- `available` : Leçon disponible et accessible
- `coming-soon` : Leçon en développement
- `completed` : Leçon terminée par l'utilisateur

### Niveaux de difficulté

- `Débutant` : Badge vert
- `Intermédiaire` : Badge orange  
- `Avancé` : Badge rouge

## 🎨 Personnalisation

### Couleurs des catégories
- `Fondamentaux` : Badge primary (bleu)
- `DOM` : Badge secondary (violet)
- `Configuration` : Badge accent (rose)
- `Outils` : Badge info (cyan)

### Ajout de nouveaux filtres
Modifiez la fonction `createLessonsFilterBar()` dans lessons.js pour ajouter de nouveaux critères.

## 📊 Fonctionnalités avancées

### Ordre des leçons
Les leçons sont automatiquement triées par leur propriété `order` pour maintenir une progression logique.

### Suivi du progrès
Le système sauvegarde automatiquement :
- Date du dernier accès à chaque leçon
- Statut de progression
- Données stockées dans localStorage

### Filtrage intelligent
- Filtrage par catégorie
- Filtrage par difficulté
- Filtrage par statut
- Compteurs automatiques

## 🔧 API JavaScript

### Fonctions principales

- `initLessonsPage()` : Initialise la page
- `createLessonCard(lesson)` : Crée une carte leçon
- `renderLessonsList(data)` : Affiche la liste des leçons
- `filterLessons(type, value)` : Filtre les leçons
- `addNewLesson(lessonObject)` : Ajoute une nouvelle leçon
- `createQuickLesson(...)` : Création rapide de leçon

### Événements

Le système écoute automatiquement :
- `DOMContentLoaded` : Initialisation
- Changements de filtres
- Clics sur les boutons leçons

## 📝 Exemple complet

```javascript
// Ajouter une leçon complète
const nouvelleLecon = {
    id: 'promises',
    title: 'Promises JavaScript',
    description: 'Comprendre et maîtriser les Promises pour la programmation asynchrone',
    difficulty: 'Intermédiaire',
    duration: '75 min',
    topics: ['Promises', 'Async', 'JavaScript'],
    file: 'promises.html',
    status: 'available',
    badge: 'Important',
    category: 'Avancé',
    order: 8
};

addNewLesson(nouvelleLecon);
```

## 🚀 Prochaines améliorations

- [ ] Système de prérequis entre leçons
- [ ] Progression visuelle (barre de progression)
- [ ] Temps de lecture estimé
- [ ] Système de favoris
- [ ] Export/Import de progression
- [ ] Intégration avec un système de quiz
