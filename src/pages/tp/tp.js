console.log('TP PAGE');

/**
 * Configuration des TPs disponibles
 * Chaque TP contient les informations nécessaires pour générer sa carte
 */
const tpData = [
    {
        id: 'mensonge',
        title: 'TP Mensonge',
        description: 'Travaux pratiques sur la manipulation des variables et la logique conditionnelle en JavaScript.',
        difficulty: 'Débutant',
        duration: '45 min',
        topics: ['Variables', 'Conditions', 'Logique'],
        file: 'mensonge.html',
        status: 'available', // available, coming-soon, completed
        badge: 'Nouveau'
    },
    {
        id: 'user-profile',
        title: 'TP Profil Utilisateur',
        description: 'Création d\'un système de profil utilisateur avec manipulation du DOM et gestion des événements.',
        difficulty: 'Intermédiaire',
        duration: '90 min',
        topics: ['DOM', 'Événements', 'Formulaires'],
        file: 'user-profile.html',
        status: 'available',
        badge: 'Bientôt'
    },
    {
        id: 'calculator',
        title: 'TP Calculatrice',
        description: 'Développement d\'une calculatrice interactive avec gestion des opérations mathématiques.',
        difficulty: 'Intermédiaire',
        duration: '120 min',
        topics: ['Fonctions', 'Événements', 'Math'],
        file: 'calculator.html',
        status: 'coming-soon',
        badge: null
    },
    {
        id: 'todo-list',
        title: 'TP Todo List',
        description: 'Création d\'une application de gestion de tâches avec localStorage et manipulation du DOM.',
        difficulty: 'Avancé',
        duration: '150 min',
        topics: ['LocalStorage', 'CRUD', 'DOM'],
        file: 'todo-list.html',
        status: 'coming-soon',
        badge: null
    }
    // Ajoutez d'autres TPs ici au fur et à mesure
];

/**
 * Génère une carte TP avec DaisyUI
 * @param {Object} tp - Les données du TP
 * @returns {HTMLElement} L'élément carte généré
 */
function createTPCard(tp) {
    const card = document.createElement('div');
    card.className = 'card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-300';

    // Couleurs selon la difficulté
    const difficultyColors = {
        'Débutant': 'badge-success',
        'Intermédiaire': 'badge-warning',
        'Avancé': 'badge-error'
    };

    // Couleurs selon le statut
    const statusConfig = {
        'available': {
            buttonClass: 'btn-primary',
            buttonText: 'Commencer le TP',
            disabled: false
        },
        'coming-soon': {
            buttonClass: 'btn-disabled',
            buttonText: 'Bientôt disponible',
            disabled: true
        },
        'completed': {
            buttonClass: 'btn-success',
            buttonText: 'Revoir le TP',
            disabled: false
        }
    };

    const config = statusConfig[tp.status] || statusConfig['available'];

    card.innerHTML = `
        <div class="card-body">
            <div class="flex justify-between items-start mb-2">
                <h2 class="card-title text-lg font-bold">${tp.title}</h2>
                ${tp.badge ? `<div class="badge badge-secondary">${tp.badge}</div>` : ''}
            </div>

            <p class="text-sm text-base-content/70 mb-4">${tp.description}</p>

            <div class="flex flex-wrap gap-2 mb-4">
                ${tp.topics.map(topic => `<span class="badge badge-outline badge-sm">${topic}</span>`).join('')}
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${tp.duration}</span>
                </div>
                <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span class="badge ${difficultyColors[tp.difficulty]} badge-sm">${tp.difficulty}</span>
                </div>
            </div>

            <div class="card-actions justify-end">
                <button class="btn ${config.buttonClass} btn-sm"
                        ${config.disabled ? 'disabled' : ''}
                        onclick="openTP('${tp.file}', '${tp.id}')">
                    ${config.buttonText}
                </button>
            </div>
        </div>
    `;

    return card;
}

/**
 * Fonction pour ouvrir un TP
 * @param {string} filename - Le nom du fichier TP
 * @param {string} tpId - L'ID du TP
 */
function openTP(filename, tpId) {
    console.log(`Ouverture du TP: ${tpId} (${filename})`);

    // Rediriger vers le TP
    window.location.href = `/src/pages/tp/${filename}`;

    // Optionnel: Sauvegarder le progrès dans localStorage
    const progress = JSON.parse(localStorage.getItem('tp-progress') || '{}');
    progress[tpId] = {
        lastAccessed: new Date().toISOString(),
        status: 'started'
    };
    localStorage.setItem('tp-progress', JSON.stringify(progress));
}

/**
 * Filtre les TPs selon différents critères
 * @param {string} filterType - Type de filtre ('all', 'difficulty', 'status')
 * @param {string} filterValue - Valeur du filtre
 */
function filterTPs(filterType, filterValue) {
    let filteredData = tpData;

    if (filterType === 'difficulty' && filterValue !== 'all') {
        filteredData = tpData.filter(tp => tp.difficulty === filterValue);
    } else if (filterType === 'status' && filterValue !== 'all') {
        filteredData = tpData.filter(tp => tp.status === filterValue);
    }

    renderTPList(filteredData);
}

/**
 * Rend la liste des TPs dans le conteneur
 * @param {Array} data - Les données des TPs à afficher
 */
function renderTPList(data = tpData) {
    const container = document.getElementById('tp-container');
    if (!container) {
        console.warn('Conteneur TP non trouvé');
        return;
    }

    // Vider le conteneur
    container.innerHTML = '';

    // Ajouter les cartes
    data.forEach(tp => {
        const card = createTPCard(tp);
        container.appendChild(card);
    });

    // Afficher un message si aucun TP trouvé
    if (data.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <div class="text-6xl mb-4">📚</div>
                <h3 class="text-xl font-semibold mb-2">Aucun TP trouvé</h3>
                <p class="text-base-content/60">Essayez de modifier vos filtres ou revenez plus tard.</p>
            </div>
        `;
    }
}

/**
 * Crée la barre de filtres
 * @returns {HTMLElement} L'élément de filtres
 */
function createFilterBar() {
    const filterBar = document.createElement('div');
    filterBar.className = 'bg-base-200 rounded-lg p-4 mb-6';

    filterBar.innerHTML = `
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium">Filtrer par:</span>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Difficulté</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterTPs('difficulty', this.value)">
                    <option value="all">Toutes</option>
                    <option value="Débutant">Débutant</option>
                    <option value="Intermédiaire">Intermédiaire</option>
                    <option value="Avancé">Avancé</option>
                </select>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text text-xs">Statut</span>
                </label>
                <select class="select select-bordered select-sm" onchange="filterTPs('status', this.value)">
                    <option value="all">Tous</option>
                    <option value="available">Disponible</option>
                    <option value="coming-soon">Bientôt</option>
                    <option value="completed">Terminé</option>
                </select>
            </div>

            <div class="ml-auto">
                <div class="stats stats-horizontal shadow-sm">
                    <div class="stat py-2 px-4">
                        <div class="stat-title text-xs">Total TPs</div>
                        <div class="stat-value text-lg">${tpData.length}</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    return filterBar;
}

/**
 * Initialise la page des TPs
 */
function initTPPage() {
    console.log('Initialisation de la page TPs');

    // Trouver le conteneur principal
    const mainContainer = document.querySelector('main .container') || document.querySelector('main');
    if (!mainContainer) {
        console.error('Conteneur principal non trouvé');
        return;
    }

    // Créer la structure de la page
    const pageStructure = document.createElement('div');
    pageStructure.innerHTML = `
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-4">Travaux Pratiques</h1>
            <p class="text-lg text-base-content/70 max-w-3xl">
                Mettez en pratique vos connaissances JavaScript avec nos exercices interactifs.
                Chaque TP est conçu pour renforcer des concepts spécifiques et vous faire progresser étape par étape.
            </p>
        </div>

        <div id="filter-container"></div>

        <div id="tp-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Les cartes TP seront générées ici -->
        </div>
    `;

    // Ajouter la structure à la page
    mainContainer.appendChild(pageStructure);

    // Ajouter la barre de filtres
    const filterContainer = document.getElementById('filter-container');
    if (filterContainer) {
        filterContainer.appendChild(createFilterBar());
    }

    // Rendre la liste des TPs
    renderTPList();

    // Charger le progrès depuis localStorage
    loadProgress();
}

/**
 * Charge le progrès des TPs depuis localStorage
 */
function loadProgress() {
    const progress = JSON.parse(localStorage.getItem('tp-progress') || '{}');

    // Mettre à jour le statut des TPs selon le progrès
    tpData.forEach(tp => {
        if (progress[tp.id]) {
            const tpProgress = progress[tp.id];
            if (tpProgress.status === 'completed') {
                tp.status = 'completed';
            }
        }
    });
}

/**
 * Fonction utilitaire pour ajouter un nouveau TP
 * @param {Object} newTP - Les données du nouveau TP
 */
function addNewTP(newTP) {
    // Validation basique
    const requiredFields = ['id', 'title', 'description', 'difficulty', 'duration', 'topics', 'file'];
    const missingFields = requiredFields.filter(field => !newTP[field]);

    if (missingFields.length > 0) {
        console.error('Champs manquants pour le nouveau TP:', missingFields);
        return false;
    }

    // Vérifier que l'ID est unique
    if (tpData.find(tp => tp.id === newTP.id)) {
        console.error('Un TP avec cet ID existe déjà:', newTP.id);
        return false;
    }

    // Ajouter les valeurs par défaut
    const tpWithDefaults = {
        status: 'available',
        badge: null,
        ...newTP
    };

    // Ajouter à la liste
    tpData.push(tpWithDefaults);

    // Re-rendre la liste
    renderTPList();

    console.log('Nouveau TP ajouté:', newTP.id);
    return true;
}

// Rendre les fonctions globales pour pouvoir les utiliser dans le HTML
window.openTP = openTP;
window.filterTPs = filterTPs;
window.addNewTP = addNewTP;

/**
 * Fonction utilitaire pour créer rapidement un nouveau TP
 * Utilisez cette fonction dans la console pour ajouter facilement de nouveaux TPs
 *
 * Exemple d'utilisation :
 * createQuickTP('api-fetch', 'TP API Fetch', 'Apprendre à utiliser fetch pour récupérer des données', 'Intermédiaire', '60 min', ['API', 'Fetch', 'Async'])
 */
function createQuickTP(id, title, description, difficulty = 'Débutant', duration = '45 min', topics = []) {
    const newTP = {
        id: id,
        title: title,
        description: description,
        difficulty: difficulty,
        duration: duration,
        topics: topics,
        file: `${id}.html`,
        status: 'coming-soon',
        badge: 'Nouveau'
    };

    return addNewTP(newTP);
}

// Rendre la fonction globale
window.createQuickTP = createQuickTP;

// Initialiser quand le DOM est prêt
document.addEventListener('DOMContentLoaded', initTPPage);

// Afficher des informations utiles dans la console
console.log(`
🚀 Système de TP automatisé initialisé !

📚 TPs disponibles: ${tpData.length}

🛠️ Fonctions utilitaires disponibles:
- createQuickTP(id, title, description, difficulty, duration, topics) : Créer rapidement un nouveau TP
- addNewTP(tpObject) : Ajouter un TP avec un objet complet
- filterTPs(type, value) : Filtrer les TPs

💡 Exemple pour ajouter un nouveau TP:
createQuickTP('mon-tp', 'Mon Nouveau TP', 'Description du TP', 'Débutant', '30 min', ['JavaScript', 'DOM'])
`);

//!---------------------------------------------------------------------------
//!----------------------- TP Compteur de mensonges persistant----------------
//!---------------------------------------------------------------------------
// const btnIncrement = document.querySelector('#increment-btn');
// const btnReset = document.querySelector('#reset-btn');
// const mensongeCounter = document.querySelector('#mensonge-counter');
// const mensongeProgressBar = document.querySelector('#mensonge-progress');
// mensongeProgressBar.value = 0;

// // Step 1 on arrive dans la page on essais de récupérer le nombre de mensonge dans le localStorage sinon 0 
// mensongeCounter.innerText = localStorage.getItem('mensonge-counter') || 0;

// btnIncrement.addEventListener('click', () => {
//    // console.log('click');
   
//    //  let counter = parseInt(mensongeCounter.innerText);
//     let counter = mensongeCounter.innerText;
//     counter++;
//     mensongeCounter.innerText = counter;
//     localStorage.setItem('mensonge-counter', counter);
// });

// btnReset.addEventListener('click', () => {
//    alert('Interdit Sale Tricheur !');
// });

// MENSONGE avec Progress Bar
const btnIncrement = document.querySelector('#increment-btn');
const btnReset = document.querySelector('#reset-btn');
const mensongeCounter = document.querySelector('#mensonge-counter');
const mensongeProgressBar = document.querySelector('#mensonge-progress');

// Fonction pour mettre à jour la progress bar
function updateProgressBar(counter) {
    // Calcule le pourcentage (max 100)
    const progressValue = Math.min((counter / 100) * 100, 100);
    mensongeProgressBar.value = progressValue;

    console.log(`Progress bar mise à jour: ${progressValue}%`);
}

// Initialisation au chargement de la page
const savedCounter = parseInt(localStorage.getItem('mensonge-counter')) || 0;
mensongeCounter.innerText = savedCounter;
updateProgressBar(savedCounter);

btnIncrement.addEventListener('click', () => {
    console.log('click');

    let counter = parseInt(mensongeCounter.innerText);
    counter++;
    mensongeCounter.innerText = counter;
    localStorage.setItem('mensonge-counter', counter);

    // Mise à jour de la progress bar
    updateProgressBar(counter);
});

btnReset.addEventListener('click', () => {
    if (confirm('Êtes-vous sûr de vouloir remettre le compteur à zéro ?')) {
        mensongeCounter.innerText = 0;
        localStorage.setItem('mensonge-counter', 0);
        updateProgressBar(0);
        console.log('Compteur remis à zéro');
    }
});